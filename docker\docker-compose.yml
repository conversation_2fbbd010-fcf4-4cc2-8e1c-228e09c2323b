services:
  etcd:
    image: quay.io/coreos/etcd:v3.5.18
    container_name: milvus-etcd
    command: >
      etcd
      -advertise-client-urls=http://etcd:2379
      -listen-client-urls http://0.0.0.0:2379
      --data-dir /etcd
    volumes:
      - ./volumes/etcd:/etcd

  minio:
    image: minio/minio:RELEASE.2024-12-18T13-15-44Z
    container_name: milvus-minio
    environment:
      MINIO_ROOT_USER: minioadmin      
      MINIO_ROOT_PASSWORD: minioadmin   
    command: server /minio_data --console-address ":9001"
    ports:
      - "9000:9000" 
      - "9001:9001"  
    volumes:
      - ./volumes/minio:/minio_data

  milvus:
    image: milvusdb/milvus:v2.6.0
    container_name: milvus
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
      MQ_TYPE: woodpecker
      MILVUS_AUTH_ENABLE: "true"       
      MILVUS_AUTH_USER: "admin"           
      MILVUS_AUTH_PASSWORD: "123456"     
    depends_on:
      - etcd
      - minio
    ports:
      - "19530:19530" 
      - "9091:9091"   
    volumes:
      - ./volumes/milvus:/var/lib/milvus

  attu:
    image: zilliz/attu:latest
    container_name: attu
    environment:
      MILVUS_URL: milvus:19530
    depends_on:
      - milvus
    ports:
      - "8000:3000" 

  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: chatdb
      MYSQL_USER: admin
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - ./volumes/mysql:/var/lib/mysql

networks:
  default:
    name: milvus
