# etl/crawl/crawl_news.py
# -*- coding: utf-8 -*-
import argparse
import asyncio
import json
import pathlib
import time
import uuid
from datetime import datetime
from typing import Dict, List, Set, Optional

import feedparser
import yaml

from crawl4ai import AsyncWebCrawler
from crawl4ai.async_configs import BrowserConfig, CrawlerRunConfig


# ========= Paths & Constants =========
ROOT = pathlib.Path(__file__).resolve().parents[2]   # milvus-chat/
CONF = ROOT / "conf" / "sources.yml"
OUT_DIR = ROOT / "data" / "raw"
OUT_DIR.mkdir(parents=True, exist_ok=True)

DEFAULT_OUT = OUT_DIR / "news_raw.jsonl"
DEFAULT_UA = "MilvusNewsCrawler/1.0 (+contact: <EMAIL>)"


# ========= Helpers =========
def load_sources(conf_path: pathlib.Path) -> List[Dict]:
    if not conf_path.exists():
        raise FileNotFoundError(f"Missing config: {conf_path}")
    with open(conf_path, "r", encoding="utf-8") as f:
        data = yaml.safe_load(f) or {}
    sources = data.get("sources", [])
    if not sources:
        raise ValueError("No sources found in conf/sources.yml")
    return sources


def read_seen_urls(jsonl_path: pathlib.Path) -> Set[str]:
    """Đọc các URL đã lưu để tránh crawl trùng."""
    seen: Set[str] = set()
    if jsonl_path.exists():
        with open(jsonl_path, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    rec = json.loads(line)
                    u = rec.get("url")
                    if u:
                        seen.add(u)
                except Exception:
                    pass
    return seen


def rss_to_items(feed_url: str) -> List[Dict]:
    """Parse 1 RSS -> list {url, published_at}"""
    d = feedparser.parse(feed_url)
    items: List[Dict] = []
    for e in d.entries:
        url = getattr(e, "link", None)
        if not url:
            continue
        ts = None
        for key in ("published_parsed", "updated_parsed", "created_parsed"):
            val = getattr(e, key, None)
            if val:
                try:
                    ts = int(time.mktime(val))
                    break
                except Exception:
                    pass
        items.append({"url": url, "published_at": ts})
    return items


# ========= Crawl =========
async def fetch_article(
    crawler: AsyncWebCrawler,
    url: str,
    timeout: int = 25,
) -> Optional[Dict]:
    """
    Trả về {title, markdown} hoặc None nếu thất bại/ngắn quá.
    """
    run_cfg = CrawlerRunConfig(
        extraction_strategy="readability",
        remove_overlay=True,
        screenshot=False,
        timeout=timeout,
    )
    try:
        res = await crawler.arun(url=url, config=run_cfg)
        title = (res.title or "").strip()
        markdown = (res.markdown or "").strip()
        if len(markdown) < 200:  # bỏ bài quá ngắn
            return None
        return {"title": title, "markdown": markdown}
    except Exception as ex:
        print(f"[ERR] Crawl failed: {url} -> {ex}")
        return None


async def main():
    parser = argparse.ArgumentParser(description="Crawl news -> data/raw/news_raw.jsonl")
    parser.add_argument("--only", type=str, default=None, help="Chỉ crawl nguồn có name=... trong conf/sources.yml")
    parser.add_argument("--limit", type=int, default=150, help="Giới hạn tổng số bài")
    parser.add_argument("--delay", type=float, default=0.6, help="Delay giữa các request (giây)")
    parser.add_argument("--refresh", action="store_true", help="Bỏ qua cache URL đã có (crawl lại)")
    parser.add_argument("--out", type=str, default=str(DEFAULT_OUT), help="Đường dẫn file JSONL đầu ra")
    args = parser.parse_args()

    out_path = pathlib.Path(args.out)
    sources = load_sources(CONF)

    if args.only:
        sources = [s for s in sources if s.get("name") == args.only]
        if not sources:
            print(f"[WARN] No source matched --only={args.only} in {CONF}")
            return

    seen = set() if args.refresh else read_seen_urls(out_path)

    # Thu thập URL từ tất cả feed
    url_rows: List[Dict] = []
    for src in sources:
        name = src.get("name")
        feeds = src.get("feeds", [])
        for feed in feeds:
            try:
                items = rss_to_items(feed)
                for it in items:
                    url_rows.append({
                        "url": it["url"],
                        "published_at": it["published_at"],
                        "source": name,
                        "lang": src.get("lang", "vi"),
                        "category": src.get("category", "general"),
                    })
            except Exception as ex:
                print(f"[ERR] RSS parse failed: {feed} -> {ex}")

    # Dedup theo URL, giữ thứ tự xuất hiện
    deduped: List[Dict] = []
    tmp = set()
    for r in url_rows:
        u = r["url"]
        if u in tmp:
            continue
        tmp.add(u)
        deduped.append(r)

    if args.limit and len(deduped) > args.limit:
        deduped = deduped[:args.limit]

    # Tạo crawler async
    browser_cfg = BrowserConfig(headless=True, user_agent=DEFAULT_UA)
    saved = 0
    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        # Mở file bằng context manager thường (không async)
        with open(out_path, "a", encoding="utf-8") as out:
            for row in deduped:
                url = row["url"]
                if url in seen:
                    continue

                art = await fetch_article(crawler, url)
                if not art:
                    continue

                record = {
                    "article_id": str(uuid.uuid4()),
                    "url": url,
                    "title": art["title"],
                    "text_markdown": art["markdown"],
                    "source": row["source"],
                    "lang": row["lang"],
                    "category": row["category"],
                    "published_at": row["published_at"],   # epoch sec hoặc None
                    "fetched_at": int(time.time()),
                    "date_str": datetime.utcnow().strftime("%Y-%m-%d"),
                }
                out.write(json.dumps(record, ensure_ascii=False) + "\n")
                saved += 1
                seen.add(url)
                print(f"[OK] {url}")
                await asyncio.sleep(args.delay)

    print(f"\nDone. Saved {saved} articles -> {out_path}")


if __name__ == "__main__":
    asyncio.run(main())
